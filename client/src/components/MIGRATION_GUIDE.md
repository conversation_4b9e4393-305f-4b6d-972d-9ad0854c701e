# Atomic Design Migration Guide

## Overview

This guide provides step-by-step instructions for migrating from legacy UI components to the new atomic design system. The migration ensures consistency, accessibility, and electrical engineering domain integration.

## Migration Strategy

### Phase 1: Atomic Components (COMPLETED ✅)
- ✅ Button → Enhanced with electrical variants
- ✅ Input → Validation states and electrical contexts  
- ✅ Label → Professional typography standards
- ✅ Icon → Electrical engineering icon set
- ✅ Badge → Status and category indicators
- ✅ StatusIndicator → Professional electrical status display
- ✅ Avatar → Electrical engineering role integration
- ✅ ProgressBar/CircularProgress → System process indicators
- ✅ Chip → Component categorization and filtering

### Phase 2: Molecular Components (COMPLETED ✅)
- ✅ InputField → Complete form fields with validation
- ✅ ButtonGroup → System control groupings
- ✅ AlertCard → Professional system notifications
- ✅ HealthIndicator → Equipment health monitoring
- ✅ SearchBox → Advanced component search
- ✅ StatusCard → Comprehensive status displays

### Phase 3: Organisms (IN PROGRESS)
- ✅ Equipment Dashboard - Complete electrical equipment monitoring interface
- ✅ Control Panels - Professional electrical system control interfaces  
- 🔄 Project Navigation - Electrical team project management
- 🔄 System Configuration - Electrical system settings and validation

## Component Migration Mapping

### Button Components

```tsx
// BEFORE: Legacy Button
import { LegacyButton } from '@/components/legacy'
<LegacyButton type="primary" size="large" className="custom-class">
  Submit Form
</LegacyButton>

// AFTER: Atomic Button
import { Button } from '@/components/atoms'
<Button variant="default" size="lg" className="custom-class">
  Submit Form
</Button>

// ELECTRICAL CONTEXT: Enhanced for electrical systems
<Button variant="electrical" size="lg">
  System Control
</Button>
```

**Migration Steps:**
1. Replace import path: `@/components/legacy` → `@/components/atoms`
2. Update props: `type="primary"` → `variant="default"`
3. Update props: `size="large"` → `size="lg"`
4. Add electrical variants where appropriate

### Input Components

```tsx
// BEFORE: Legacy Input
import { LegacyInput } from '@/components/legacy'
<LegacyInput 
  placeholder="Enter value" 
  error="Invalid input"
  success={true}
/>

// AFTER: Atomic Input
import { Input } from '@/components/atoms'
<Input 
  placeholder="Enter value"
  state="error" // or "success", "warning"
  variant="default"
/>

// ELECTRICAL CONTEXT: Enhanced for electrical measurements
<Input 
  placeholder="Enter voltage (V)"
  variant="electrical"
  state="success"
/>
```

**Migration Steps:**
1. Replace import path
2. Consolidate error/success props into `state` prop
3. Add `variant` prop for electrical contexts
4. Use electrical-specific placeholders and validation

### Status Display Components

```tsx
// BEFORE: Legacy Status
import { LegacyStatus } from '@/components/legacy'
<LegacyStatus status="active" color="green" size="medium" />

// AFTER: Status Indicator
import { StatusIndicator } from '@/components/atoms'
<StatusIndicator 
  status="operational" 
  size="md" 
  style="dot" 
/>

// ELECTRICAL CONTEXT: Professional electrical status
<StatusIndicator 
  status="energized" 
  size="lg" 
  style="led" 
/>
```

**Migration Steps:**
1. Replace component: `LegacyStatus` → `StatusIndicator`
2. Map status values: `active` → `operational`, `inactive` → `offline`
3. Replace color prop with professional status values
4. Add electrical status types: `energized`, `de_energized`, `fault`

### User Display Components

```tsx
// BEFORE: Legacy User Avatar
import { LegacyAvatar } from '@/components/legacy'
<LegacyAvatar 
  name="John Smith" 
  role="Engineer" 
  status="online"
  size="large"
/>

// AFTER: Atomic Avatar
import { Avatar } from '@/components/atoms'
<Avatar 
  fallback="JS"
  role="Electrical Engineer"
  status="online"
  size="lg"
/>

// ELECTRICAL CONTEXT: Role-specific avatars
<Avatar 
  role="Lead Engineer"
  status="online"
  size="lg"
  fallback="LE"
/>
```

**Migration Steps:**
1. Replace component: `LegacyAvatar` → `Avatar`
2. Add `fallback` prop for initials
3. Use specific electrical engineering roles
4. Update size values: `large` → `lg`, `small` → `sm`

### Form Field Components

```tsx
// BEFORE: Legacy Form Field
import { LegacyFormField } from '@/components/legacy'
<LegacyFormField 
  label="Equipment ID"
  input={{
    placeholder: "Enter ID",
    error: "Required field"
  }}
  required={true}
/>

// AFTER: Molecular InputField
import { InputField } from '@/components/molecules'
<InputField 
  label="Equipment ID"
  placeholder="Enter equipment identifier"
  state="error"
  helperText="Required field"
  required
  variant="electrical"
/>
```

**Migration Steps:**
1. Replace component: `LegacyFormField` → `InputField`
2. Flatten nested input props to top level
3. Use `state` and `helperText` for validation feedback
4. Add electrical variant for engineering contexts

### Alert/Notification Components

```tsx
// BEFORE: Legacy Alert
import { LegacyAlert } from '@/components/legacy'
<LegacyAlert 
  type="warning"
  title="System Alert"
  message="High temperature detected"
  dismissible={true}
/>

// AFTER: Molecular AlertCard
import { AlertCard } from '@/components/molecules'
<AlertCard 
  variant="warning"
  title="System Alert"
  description="High temperature detected"
  dismissible
  onDismiss={() => handleDismiss()}
/>

// ELECTRICAL CONTEXT: Professional electrical alerts
import { ElectricalAlertCard } from '@/components/molecules'
<ElectricalAlertCard
  title="Over Current Protection Activated"
  description="Circuit breaker CB-101 has tripped"
  equipmentId="CB-101"
  severity="critical"
/>
```

**Migration Steps:**
1. Replace component: `LegacyAlert` → `AlertCard`
2. Rename prop: `message` → `description`
3. Add onDismiss handler for dismissible alerts
4. Use electrical-specific alert variants for system notifications

## Electrical Engineering Enhancements

### Status Management
The new system provides professional electrical status management:

```tsx
// Professional electrical statuses
<StatusIndicator status="energized" style="led" />      // System powered
<StatusIndicator status="de_energized" style="dot" />   // System isolated
<StatusIndicator status="fault" style="icon" />         // System fault
<StatusIndicator status="maintenance" style="dot" />    // Under maintenance
<StatusIndicator status="testing" style="led" />        // Testing mode
<StatusIndicator status="commissioning" style="icon" /> // Being commissioned
```

### Role-Based Access
Enhanced role management for electrical teams:

```tsx
// Electrical engineering roles
<Avatar role="Project Manager" />           // Project oversight
<Avatar role="Lead Engineer" />             // Technical leadership  
<Avatar role="Electrical Engineer" />       // Design and analysis
<Avatar role="Automation Engineer" />       // Control systems
<Avatar role="Instrumentation Engineer" />  // Measurement systems
<Avatar role="CAD Operator" />             // Drawing production
```

### Equipment Health Monitoring
Professional equipment health indicators:

```tsx
// Motor health monitoring
<HealthIndicator
  status="healthy"
  metrics={[
    { name: 'Temperature', value: 65, unit: '°C', status: 'normal' },
    { name: 'Vibration', value: 2.1, unit: 'mm/s', status: 'warning' },
    { name: 'Current', value: 45.2, unit: 'A', status: 'normal' }
  ]}
/>

// Power system status
<StatusCard
  title="Main Distribution Panel"
  status="operational"
  metrics={[
    { name: 'Load', value: 75, unit: '%', status: 'normal' },
    { name: 'Voltage', value: 480, unit: 'V', status: 'normal' }
  ]}
/>
```

## Migration Checklist

### Pre-Migration
- [ ] Audit existing components and their usage patterns
- [ ] Identify electrical engineering-specific contexts
- [ ] Create component inventory with migration priorities
- [ ] Set up testing strategy for migrated components

### During Migration
- [ ] Update import statements to atomic design paths
- [ ] Replace legacy prop names with atomic design props
- [ ] Add electrical variants where appropriate
- [ ] Update TypeScript types and interfaces
- [ ] Migrate styles to use design system classes
- [ ] Add accessibility attributes (WCAG 2.1 AA)
- [ ] Test component functionality and visual appearance
- [ ] Update documentation and usage examples

### Post-Migration
- [ ] Remove legacy component imports and dependencies  
- [ ] Update component tests to use new atomic components
- [ ] Verify electrical engineering contexts work correctly
- [ ] Conduct accessibility testing
- [ ] Performance testing for new component implementations
- [ ] Update component documentation and guides
- [ ] Train team on new atomic design patterns

## Automated Migration Tools

### Find and Replace Patterns

Use these regex patterns to assist with bulk migration:

```bash
# Replace legacy imports
find src/ -name "*.tsx" -o -name "*.ts" | xargs sed -i 's/@\/components\/legacy/@\/components\/atoms/g'

# Replace button props
find src/ -name "*.tsx" | xargs sed -i 's/type="primary"/variant="default"/g'
find src/ -name "*.tsx" | xargs sed -i 's/size="large"/size="lg"/g'
find src/ -name "*.tsx" | xargs sed -i 's/size="small"/size="sm"/g'

# Replace status props
find src/ -name "*.tsx" | xargs sed -i 's/status="active"/status="operational"/g'
find src/ -name "*.tsx" | xargs sed -i 's/status="inactive"/status="offline"/g'
```

### TypeScript Migration Helper

Create a TypeScript transformation script:

```typescript
// migration-helper.ts
interface LegacyProps {
  type?: 'primary' | 'secondary' | 'danger'
  size?: 'small' | 'medium' | 'large'
  status?: 'active' | 'inactive' | 'warning'
}

interface AtomicProps {
  variant?: 'default' | 'destructive' | 'outline' | 'electrical'
  size?: 'sm' | 'default' | 'lg'
  status?: 'operational' | 'offline' | 'warning' | 'critical'
}

function migrateLegacyProps(legacy: LegacyProps): AtomicProps {
  return {
    variant: legacy.type === 'primary' ? 'default' : 
             legacy.type === 'danger' ? 'destructive' : 'outline',
    size: legacy.size === 'large' ? 'lg' : 
          legacy.size === 'small' ? 'sm' : 'default',
    status: legacy.status === 'active' ? 'operational' :
            legacy.status === 'inactive' ? 'offline' : legacy.status
  }
}
```

## Testing Migration

### Component Testing
Test each migrated component:

```tsx
// Test atomic component migration
describe('Button Migration', () => {
  it('should render with electrical variant', () => {
    render(<Button variant="electrical">System Control</Button>)
    expect(screen.getByRole('button')).toHaveClass('bg-electrical-600')
  })

  it('should maintain accessibility', () => {
    render(<Button variant="electrical">Emergency Stop</Button>)
    expect(screen.getByRole('button')).toBeAccessible()
  })
})
```

### Visual Regression Testing
Use visual testing to ensure design consistency:

```tsx
// Storybook stories for visual testing
export const ElectricalComponents = {
  render: () => (
    <div className="space-y-4">
      <Button variant="electrical">System Control</Button>
      <StatusIndicator status="energized" style="led" />
      <Avatar role="Lead Engineer" fallback="LE" />
    </div>
  )
}
```

## Rollback Strategy

### Component Aliasing
Maintain compatibility during migration:

```tsx
// components/legacy/index.ts - Compatibility layer
export { Button as LegacyButton } from '../atoms/Button'
export { StatusIndicator as LegacyStatus } from '../atoms/StatusIndicator'

// Allows gradual migration without breaking existing code
```

### Feature Flags
Use feature flags for gradual rollout:

```tsx
// Use feature flag for atomic design
const useAtomicDesign = useFeatureFlag('atomic-design-migration')

return useAtomicDesign ? (
  <Button variant="electrical">New Atomic Button</Button>
) : (
  <LegacyButton type="primary">Legacy Button</LegacyButton>
)
```

## Performance Considerations

### Bundle Size Optimization
The atomic design system is optimized for tree-shaking:

```tsx
// Good - Tree-shakable imports
import { Button, StatusIndicator } from '@/components/atoms'

// Avoid - Full module imports
import * as Atoms from '@/components/atoms'
```

### Component Lazy Loading
Implement lazy loading for larger molecular components:

```tsx
// Lazy load complex molecules
const HealthIndicator = React.lazy(() => 
  import('@/components/molecules/HealthIndicator')
)

// Use with Suspense
<React.Suspense fallback={<div>Loading...</div>}>
  <HealthIndicator {...props} />
</React.Suspense>
```

## Success Metrics

### Migration Completion
- [ ] 100% of legacy components replaced
- [ ] All TypeScript errors resolved
- [ ] All tests passing with atomic components
- [ ] Visual consistency maintained across application

### Quality Improvements
- [ ] WCAG 2.1 AA accessibility compliance
- [ ] Electrical engineering domain integration
- [ ] Professional status management implementation
- [ ] Role-based access control integration

### Performance Gains
- [ ] Reduced bundle size through tree-shaking
- [ ] Improved component reusability
- [ ] Consistent design system implementation
- [ ] Enhanced maintainability and scalability

## Support and Resources

### Documentation
- [Atomic Design Guide](./ATOMIC_DESIGN_GUIDE.md) - Complete usage documentation
- [Component API Reference](./atoms/index.ts) - TypeScript definitions
- [Test Examples](./atoms/__tests__/) - Component testing patterns

### Team Support
- Migration workshops for development team
- Code review guidelines for atomic design patterns
- Best practices documentation for electrical contexts
- Troubleshooting guide for common migration issues

## Organism Migration Strategy

### Equipment Dashboard Migration

#### Legacy Component Replacement
Replace multiple legacy dashboard components with the unified Equipment Dashboard organism:

```tsx
// BEFORE: Legacy Dashboard Components
import { 
  LegacyEquipmentGrid,
  LegacyAlertPanel,
  LegacyFilterSidebar,
  LegacyStatusCards
} from '@/components/legacy'

function LegacyEquipmentView() {
  return (
    <div className="dashboard-container">
      <LegacyFilterSidebar filters={filters} onFilterChange={handleFilterChange} />
      <div className="main-content">
        <LegacyStatusCards equipment={equipment} />
        <LegacyEquipmentGrid equipment={equipment} onSelect={handleSelect} />
      </div>
      <LegacyAlertPanel alerts={alerts} onDismiss={handleDismiss} />
    </div>
  )
}

// AFTER: Equipment Dashboard Organism
import { EquipmentDashboard } from '@/components/organisms'

function ModernEquipmentView() {
  return (
    <EquipmentDashboard
      equipment={equipment}
      alerts={alerts}
      filters={filters}
      showSearch
      showFilters
      showAlerts
      layout="grid"
      onEquipmentSelect={handleSelect}
      onAlertDismiss={handleDismiss}
      onFilterChange={handleFilterChange}
      aria-label="Equipment Monitoring Dashboard"
    />
  )
}
```

#### Migration Steps for Equipment Dashboard
1. **Audit Legacy Components**: Identify all dashboard-related components
2. **Data Structure Migration**: Convert legacy equipment data to new format
3. **Event Handler Consolidation**: Combine multiple handlers into organism props
4. **State Management Integration**: Connect React Query and Zustand stores
5. **Accessibility Enhancement**: Add ARIA labels and keyboard navigation
6. **Testing Migration**: Update tests to use organism patterns

### Control Panels Migration

#### Legacy Control Interface Replacement
Replace multiple control components with the unified Control Panels organism:

```tsx
// BEFORE: Multiple Legacy Control Components
import {
  LegacyControlButtons,
  LegacyStatusDisplay,
  LegacyInterlockPanel,
  LegacySequenceControls,
  LegacyEmergencyStop
} from '@/components/legacy'

function LegacyControlInterface() {
  return (
    <div className="control-interface">
      <LegacyStatusDisplay equipment={equipment} />
      <div className="control-section">
        <LegacyControlButtons 
          actions={actions} 
          onAction={handleAction}
          disabled={hasActiveInterlocks}
        />
        <LegacySequenceControls 
          sequences={sequences}
          onStart={startSequence}
        />
      </div>
      <LegacyInterlockPanel 
        interlocks={interlocks}
        onBypass={handleBypass}
      />
      <LegacyEmergencyStop onStop={emergencyShutdown} />
    </div>
  )
}

// AFTER: Control Panels Organism
import { ControlPanels } from '@/components/organisms'

function ModernControlInterface() {
  return (
    <ControlPanels
      controlGroups={controlGroups}
      equipment={equipment}
      interlocks={interlocks}
      alerts={controlAlerts}
      permissionLevel="engineer"
      operatorId={currentOperator.id}
      showInterlocks
      showSequences
      enableEmergencyControls
      onControlAction={handleControlAction}
      onSequenceStart={startControlSequence}
      onEmergencyStop={emergencyShutdown}
      onInterlockBypass={handleInterlockBypass}
      aria-label="Equipment Control Interface"
    />
  )
}
```

#### Migration Steps for Control Panels
1. **Safety System Audit**: Document all existing safety interlocks and controls
2. **Permission System Migration**: Map legacy roles to new permission levels
3. **Control Action Consolidation**: Combine individual control functions
4. **Sequence Definition**: Convert manual procedures to automated sequences
5. **Emergency Procedure Integration**: Implement standardized emergency controls
6. **Compliance Validation**: Ensure IEC 61508 and NFPA 70E compliance

### Implementation Timeline

#### Immediate Actions (Week 1)
- [ ] Complete legacy component inventory
- [ ] Update import statements for completed organisms
- [ ] Migrate equipment data structures to new format
- [ ] Update control action handlers and state management

#### Short-term Migration (Weeks 2-4)
- [ ] Replace Equipment Dashboard components in main application
- [ ] Replace Control Panels components in control interfaces
- [ ] Update all TypeScript types and interfaces
- [ ] Migrate component tests to use organism patterns

#### Medium-term Integration (Weeks 5-8)
- [ ] Implement React Query integration for real-time data
- [ ] Add Zustand stores for organism-specific state
- [ ] Enhance accessibility with comprehensive ARIA support
- [ ] Performance optimization and bundle size analysis

#### Long-term Optimization (Weeks 9-12)
- [ ] Complete end-to-end testing with new organisms
- [ ] Documentation updates and team training
- [ ] Performance monitoring and optimization
- [ ] Prepare for remaining organism development

### Backward Compatibility Strategy

#### Component Aliasing
Maintain compatibility during migration with component aliases:

```tsx
// components/legacy/compatibility.ts
export { EquipmentDashboard as LegacyEquipmentGrid } from '../organisms/EquipmentDashboard'
export { ControlPanels as LegacyControlInterface } from '../organisms/ControlPanels'

// Props adapter for legacy compatibility
export const LegacyEquipmentGridAdapter = (props: LegacyEquipmentGridProps) => {
  const adaptedProps: EquipmentDashboardProps = {
    equipment: props.items,
    layout: props.viewMode === 'card' ? 'grid' : 'list',
    showSearch: props.enableSearch,
    onEquipmentSelect: props.onItemSelect
  }
  
  return <EquipmentDashboard {...adaptedProps} />
}
```

#### Feature Flag Integration
Implement gradual rollout with feature flags:

```tsx
// Use feature flag for organism migration
const useOrganismMigration = useFeatureFlag('organism-migration-v2')

return useOrganismMigration ? (
  <EquipmentDashboard
    equipment={equipment}
    alerts={alerts}
    showSearch
    showFilters
    onEquipmentSelect={handleSelect}
  />
) : (
  <LegacyEquipmentGrid
    items={equipment}
    viewMode="card"
    onItemSelect={handleSelect}
  />
)
```

### Quality Assurance for Organism Migration

#### Testing Strategy
```tsx
// Test organism migration compatibility
describe('Equipment Dashboard Migration', () => {
  it('should maintain equivalent functionality to legacy components', () => {
    const legacyProps = {
      items: mockEquipment,
      viewMode: 'card',
      enableSearch: true
    }
    
    const organismProps = {
      equipment: mockEquipment,
      layout: 'grid',
      showSearch: true
    }
    
    // Test functional equivalence
    expect(getLegacyBehavior(legacyProps)).toEqual(getOrganismBehavior(organismProps))
  })

  it('should preserve all electrical engineering workflows', () => {
    render(<EquipmentDashboard {...electricalProps} />)
    
    // Test electrical-specific functionality
    expect(screen.getByLabelText('Equipment Status')).toBeInTheDocument()
    expect(screen.getByText('480V')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Emergency Stop' })).toBeInTheDocument()
  })
})
```

#### Performance Validation
- Bundle size comparison between legacy and organism implementations
- Runtime performance metrics for complex dashboards
- Memory usage analysis for large equipment datasets
- Network efficiency for real-time data updates

#### Accessibility Compliance
- WCAG 2.1 AA compliance verification for all organism features
- Screen reader compatibility testing
- Keyboard navigation functionality validation
- Color contrast and visual accessibility checks

The migration to atomic design organisms provides a solid foundation for building consistent, accessible, and domain-specific electrical engineering interfaces with professional-grade quality and safety standards.