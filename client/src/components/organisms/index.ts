/**
 * Organism Components - Complex Interface Sections
 * 
 * Atomic design organisms combining atoms and molecules to create
 * complete, complex interface sections with business logic.
 * 
 * Architecture:
 * - Composed of atoms and molecules
 * - Complex business logic integration
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Consistent design system integration
 * - Performance optimized
 * - Engineering-grade quality
 */

// Core Organisms
export {
  AuthForm,
  type AuthFormProps,
  type AuthFormData,
  type AuthFormSize,
  type AuthFormLayout,
  type AuthFormType,
  authFormVariants
} from "./AuthForm"

// Equipment Dashboard Organism
export {
  EquipmentDashboard,
  useEquipmentDashboard,
  type Equipment,
  type EquipmentAlert,
  type EquipmentFilters,
  type DashboardLayout,
  type DashboardConfig,
  type EquipmentDashboardProps,
  type EquipmentCardProps,
  type AlertPanelProps,
  type FilterPanelProps,
  type DashboardControlsProps,
  type EquipmentStatus,
  type ElectricalSystemType,
  type EquipmentPriority,
  type VoltageClass,
  type ElectricalMeasurement,
  type EquipmentLocation,
  type MaintenanceInfo,
  EQUIPMENT_STATUS_COLORS,
  SYSTEM_TYPE_LABELS,
  VOLTAGE_CLASS_LABELS,
  DEFAULT_DASHBOARD_LAYOUT,
  DEFAULT_DASHBOARD_CONFIG,
} from "./EquipmentDashboard"

// Control Panels Organism
export {
  ControlPanels,
  useControlPanels,
  type ControlGroup,
  type ControlEquipment,
  type ControlSession,
  type ControlAlert,
  type ControlLogEntry,
  type SafetyInterlock,
  type ControlSequenceStep,
  type ControlSystemType,
  type ControlAction,
  type ControlState,
  type SafetyIntegrityLevel,
  type ControlPermissionLevel,
  type ControlPanelsLayout,
  type ControlPanelsConfig,
  type ControlFilters,
  type ControlPanelsProps,
  type ControlGroupCardProps,
  type EquipmentControlProps,
  type SafetyInterlockPanelProps,
  type ControlEquipmentSummary,
  type ControlGroupSummary,
  CONTROL_STATE_COLORS,
  CONTROL_SYSTEM_LABELS,
  SAFETY_LEVEL_LABELS,
  PERMISSION_LEVEL_LABELS,
  DEFAULT_CONTROL_LAYOUT,
  DEFAULT_CONTROL_CONFIG,
  isActionPermitted,
  hasActiveInterlocks,
  canBypassInterlock,
} from "./ControlPanels"