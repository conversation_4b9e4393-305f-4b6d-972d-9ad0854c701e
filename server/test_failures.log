2025-08-08 01:07:53,395 - CRITICAL - Error in setup: tests/core/services/test_project_member_service.py::TestProjectMemberService::test_add_member_to_project_duplicate_entry
Error Details:
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:545: in _prepare_and_execute
    self._rows = deque(await prepared_stmt.fetch(*parameters))
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/asyncpg/prepared_stmt.py:176: in fetch
    data = await self.__bind_execute(args, 0, timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/asyncpg/prepared_stmt.py:267: in __bind_execute
    data, status, _ = await self.__do_execute(
.venv/lib/python3.13/site-packages/asyncpg/prepared_stmt.py:256: in __do_execute
    return await executor(protocol)
           ^^^^^^^^^^^^^^^^^^^^^^^^
asyncpg/protocol/protocol.pyx:206: in bind_execute
    ???
E   asyncpg.exceptions.ForeignKeyViolationError: insert or update on table "project_members" violates foreign key constraint "ProjectMember_role_id_fkey"
E   DETAIL:  Key (role_id)=(648) is not present in table "user_roles".

The above exception was the direct cause of the following exception:
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1963: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py:943: in do_execute
    cursor.execute(statement, parameters)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:580: in execute
    self._adapt_connection.await_(
.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py:132: in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py:196: in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:558: in _prepare_and_execute
    self._handle_exception(error)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:508: in _handle_exception
    self._adapt_connection._handle_exception(error)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:792: in _handle_exception
    raise translated_error from error
E   sqlalchemy.dialects.postgresql.asyncpg.AsyncAdapt_asyncpg_dbapi.IntegrityError: <class 'asyncpg.exceptions.ForeignKeyViolationError'>: insert or update on table "project_members" violates foreign key constraint "ProjectMember_role_id_fkey"
E   DETAIL:  Key (role_id)=(648) is not present in table "user_roles".

The above exception was the direct cause of the following exception:
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:696: in pytest_fixture_setup
    hook_result = yield
                  ^^^^^
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:321: in _async_fixture_wrapper
    result = runner.run(setup(), context=context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py:118: in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py:725: in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:317: in setup
    res = await fixture_function(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/conftest.py:910: in test_project_member
    member = await member_service.add_member_to_project(async_test_project.id, member_create)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/monitoring/unified_performance_monitor.py:263: in async_wrapper
    return await result
           ^^^^^^^^^^^^
src/core/services/general/project_member_service.py:86: in add_member_to_project
    new_member = await self.project_member_repo.create(member_dict)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/monitoring/unified_performance_monitor.py:263: in async_wrapper
    return await result
           ^^^^^^^^^^^^
src/core/repositories/base_repository.py:75: in create
    await self.db_session.flush()
.venv/lib/python3.13/site-packages/sqlalchemy/ext/asyncio/session.py:801: in flush
    await greenlet_spawn(self.sync_session.flush, objects=objects)
.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py:203: in greenlet_spawn
    result = context.switch(value)
             ^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4345: in flush
    self._flush(objects)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4480: in _flush
    with util.safe_reraise():
         ^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/util/langhelpers.py:224: in __exit__
    raise exc_value.with_traceback(exc_tb)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4441: in _flush
    flush_context.execute()
.venv/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py:466: in execute
    rec.execute(self)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py:642: in execute
    util.preloaded.orm_persistence.save_obj(
.venv/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py:93: in save_obj
    _emit_insert_statements(
.venv/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py:1233: in _emit_insert_statements
    result = connection.execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1415: in execute
    return meth(
.venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py:523: in _execute_on_connection
    return connection._execute_clauseelement(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1637: in _execute_clauseelement
    ret = self._execute_context(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1842: in _execute_context
    return self._exec_single_context(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1982: in _exec_single_context
    self._handle_dbapi_exception(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:2351: in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1963: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py:943: in do_execute
    cursor.execute(statement, parameters)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:580: in execute
    self._adapt_connection.await_(
.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py:132: in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py:196: in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:558: in _prepare_and_execute
    self._handle_exception(error)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:508: in _handle_exception
    self._adapt_connection._handle_exception(error)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:792: in _handle_exception
    raise translated_error from error
E   sqlalchemy.exc.IntegrityError: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.ForeignKeyViolationError'>: insert or update on table "project_members" violates foreign key constraint "ProjectMember_role_id_fkey"
E   DETAIL:  Key (role_id)=(648) is not present in table "user_roles".
E   [SQL: INSERT INTO project_members (name, user_id, project_id, role_id, is_active, assigned_at, expires_at, notes, created_at, updated_at, is_deleted, deleted_at, deleted_by_user_id) VALUES ($1::VARCHAR, $2::INTEGER, $3::INTEGER, $4::INTEGER, $5::BOOLEAN, $6::TIMESTAMP WITHOUT TIME ZONE, $7::TIMESTAMP WITHOUT TIME ZONE, $8::VARCHAR, $9::TIMESTAMP WITHOUT TIME ZONE, $10::TIMESTAMP WITHOUT TIME ZONE, $11::BOOLEAN, $12::TIMESTAMP WITHOUT TIME ZONE, $13::INTEGER) RETURNING project_members.id]
E   [parameters: ('Test Member', 1112050, 3287, 648, True, datetime.datetime(2025, 8, 7, 22, 7, 53, 83451), None, None, datetime.datetime(2025, 8, 7, 22, 7, 53, 83456), datetime.datetime(2025, 8, 7, 22, 7, 53, 83458), False, None, None)]
E   (Background on this error at: https://sqlalche.me/e/20/gkpj)
