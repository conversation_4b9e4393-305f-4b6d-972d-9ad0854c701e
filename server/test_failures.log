2025-08-08 00:51:49,114 - ERROR - Test Failed: tests/core/errors/test_middleware_safe_error_handlers.py::TestMiddlewareSafeErrorHandlers::test_handle_security_errors_safe_async_generic_exception_handling
Failure Details:
tests/core/errors/test_middleware_safe_error_handlers.py:223: in test_async_function
    raise ValueError("Generic error")
E   ValueError: Generic error

During handling of the above exception, another exception occurred:
tests/core/errors/test_middleware_safe_error_handlers.py:227: in test_handle_security_errors_safe_async_generic_exception_handling
    asyncio.run(test_async_function())
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py:195: in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py:118: in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py:725: in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
src/core/errors/unified_error_handler.py:1261: in async_wrapper
    raise HTTPException(
E   fastapi.exceptions.HTTPException: 500: Internal server error
2025-08-08 00:51:49,132 - ERROR - Test Failed: tests/core/errors/test_middleware_safe_error_handlers.py::TestMiddlewareSafeErrorHandlers::test_handle_security_errors_safe_sync_generic_exception_handling
Failure Details:
tests/core/errors/test_middleware_safe_error_handlers.py:240: in test_sync_function
    raise ValueError("Generic error")
E   ValueError: Generic error

During handling of the above exception, another exception occurred:
tests/core/errors/test_middleware_safe_error_handlers.py:244: in test_handle_security_errors_safe_sync_generic_exception_handling
    test_sync_function()
src/core/errors/unified_error_handler.py:1308: in sync_wrapper
    raise HTTPException(
E   fastapi.exceptions.HTTPException: 500: Internal server error
2025-08-08 00:51:49,145 - ERROR - Test Failed: tests/core/errors/test_middleware_safe_error_handlers.py::TestMiddlewareSafeErrorHandlers::test_handle_security_errors_safe_logging
Failure Details:
tests/core/errors/test_middleware_safe_error_handlers.py:257: in test_function
    raise ValueError("Test error")
E   ValueError: Test error

During handling of the above exception, another exception occurred:
tests/core/errors/test_middleware_safe_error_handlers.py:261: in test_handle_security_errors_safe_logging
    test_function()
src/core/errors/unified_error_handler.py:1308: in sync_wrapper
    raise HTTPException(
E   fastapi.exceptions.HTTPException: 500: Internal server error
2025-08-08 00:51:49,159 - ERROR - Test Failed: tests/core/errors/test_middleware_safe_error_handlers.py::TestMiddlewareSafeErrorHandlers::test_handle_security_errors_safe_unified_error_handler_called
Failure Details:
tests/core/errors/test_middleware_safe_error_handlers.py:273: in test_function
    raise ValueError("Test error")
E   ValueError: Test error

During handling of the above exception, another exception occurred:
tests/core/errors/test_middleware_safe_error_handlers.py:276: in test_handle_security_errors_safe_unified_error_handler_called
    test_function()
src/core/errors/unified_error_handler.py:1308: in sync_wrapper
    raise HTTPException(
E   fastapi.exceptions.HTTPException: 500: Internal server error
2025-08-08 00:51:50,063 - ERROR - Test Failed: tests/core/models/test_component_relational.py::TestComponentRelationalModel::test_component_creation_relational
Failure Details:
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1963: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py:943: in do_execute
    cursor.execute(statement, parameters)
E   psycopg2.errors.UniqueViolation: duplicate key value violates unique constraint "uq_component_manufacturer_model"
E   DETAIL:  Key (manufacturer, model_number, is_deleted)=(ABB, S203-B16, f) already exists.

The above exception was the direct cause of the following exception:
tests/core/models/test_component_relational.py:58: in test_component_creation_relational
    db_session.commit()
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:2032: in commit
    trans.commit(_to_root=True)
<string>:2: in commit
    ???
.venv/lib/python3.13/site-packages/sqlalchemy/orm/state_changes.py:139: in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:1313: in commit
    self._prepare_impl()
<string>:2: in _prepare_impl
    ???
.venv/lib/python3.13/site-packages/sqlalchemy/orm/state_changes.py:139: in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:1288: in _prepare_impl
    self.session.flush()
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4345: in flush
    self._flush(objects)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4480: in _flush
    with util.safe_reraise():
         ^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/util/langhelpers.py:224: in __exit__
    raise exc_value.with_traceback(exc_tb)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4441: in _flush
    flush_context.execute()
.venv/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py:466: in execute
    rec.execute(self)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py:642: in execute
    util.preloaded.orm_persistence.save_obj(
.venv/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py:93: in save_obj
    _emit_insert_statements(
.venv/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py:1233: in _emit_insert_statements
    result = connection.execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1415: in execute
    return meth(
.venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py:523: in _execute_on_connection
    return connection._execute_clauseelement(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1637: in _execute_clauseelement
    ret = self._execute_context(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1842: in _execute_context
    return self._exec_single_context(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1982: in _exec_single_context
    self._handle_dbapi_exception(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:2351: in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1963: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py:943: in do_execute
    cursor.execute(statement, parameters)
E   sqlalchemy.exc.IntegrityError: (psycopg2.errors.UniqueViolation) duplicate key value violates unique constraint "uq_component_manufacturer_model"
E   DETAIL:  Key (manufacturer, model_number, is_deleted)=(ABB, S203-B16, f) already exists.
E   
E   [SQL: INSERT INTO components (manufacturer, model_number, description, component_type_id, category_id, specifications, unit_price, currency, supplier, part_number, weight_kg, dimensions_json, is_active, is_preferred, stock_status, version, metadata_json, name, notes, created_at, updated_at, is_deleted, deleted_at, deleted_by_user_id) VALUES (%(manufacturer)s, %(model_number)s, %(description)s, %(component_type_id)s, %(category_id)s, %(specifications)s, %(unit_price)s, %(currency)s, %(supplier)s, %(part_number)s, %(weight_kg)s, %(dimensions_json)s, %(is_active)s, %(is_preferred)s, %(stock_status)s, %(version)s, %(metadata_json)s, %(name)s, %(notes)s, %(created_at)s, %(updated_at)s, %(is_deleted)s, %(deleted_at)s, %(deleted_by_user_id)s) RETURNING components.id]
E   [parameters: {'manufacturer': 'ABB', 'model_number': 'S203-B16', 'description': None, 'component_type_id': 2242, 'category_id': 3456, 'specifications': None, 'unit_price': None, 'currency': 'EUR', 'supplier': None, 'part_number': None, 'weight_kg': None, 'dimensions_json': None, 'is_active': True, 'is_preferred': False, 'stock_status': 'available', 'version': '1.0', 'metadata_json': None, 'name': 'Test Circuit Breaker', 'notes': None, 'created_at': datetime.datetime(2025, 8, 7, 21, 51, 49, 716913), 'updated_at': datetime.datetime(2025, 8, 7, 21, 51, 49, 716915), 'is_deleted': False, 'deleted_at': None, 'deleted_by_user_id': None}]
E   (Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-08 00:51:50,387 - ERROR - Test Failed: tests/core/models/test_component_relational.py::TestComponentRelationalModel::test_component_relationships
Failure Details:
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1963: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py:943: in do_execute
    cursor.execute(statement, parameters)
E   psycopg2.errors.UniqueViolation: duplicate key value violates unique constraint "uq_component_manufacturer_model"
E   DETAIL:  Key (manufacturer, model_number, is_deleted)=(Test Manufacturer, TEST-001, f) already exists.

The above exception was the direct cause of the following exception:
tests/core/models/test_component_relational.py:87: in test_component_relationships
    db_session.commit()
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:2032: in commit
    trans.commit(_to_root=True)
<string>:2: in commit
    ???
.venv/lib/python3.13/site-packages/sqlalchemy/orm/state_changes.py:139: in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:1313: in commit
    self._prepare_impl()
<string>:2: in _prepare_impl
    ???
.venv/lib/python3.13/site-packages/sqlalchemy/orm/state_changes.py:139: in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:1288: in _prepare_impl
    self.session.flush()
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4345: in flush
    self._flush(objects)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4480: in _flush
    with util.safe_reraise():
         ^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/util/langhelpers.py:224: in __exit__
    raise exc_value.with_traceback(exc_tb)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4441: in _flush
    flush_context.execute()
.venv/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py:466: in execute
    rec.execute(self)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py:642: in execute
    util.preloaded.orm_persistence.save_obj(
.venv/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py:93: in save_obj
    _emit_insert_statements(
.venv/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py:1233: in _emit_insert_statements
    result = connection.execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1415: in execute
    return meth(
.venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py:523: in _execute_on_connection
    return connection._execute_clauseelement(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1637: in _execute_clauseelement
    ret = self._execute_context(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1842: in _execute_context
    return self._exec_single_context(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1982: in _exec_single_context
    self._handle_dbapi_exception(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:2351: in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1963: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py:943: in do_execute
    cursor.execute(statement, parameters)
E   sqlalchemy.exc.IntegrityError: (psycopg2.errors.UniqueViolation) duplicate key value violates unique constraint "uq_component_manufacturer_model"
E   DETAIL:  Key (manufacturer, model_number, is_deleted)=(Test Manufacturer, TEST-001, f) already exists.
E   
E   [SQL: INSERT INTO components (manufacturer, model_number, description, component_type_id, category_id, specifications, unit_price, currency, supplier, part_number, weight_kg, dimensions_json, is_active, is_preferred, stock_status, version, metadata_json, name, notes, created_at, updated_at, is_deleted, deleted_at, deleted_by_user_id) VALUES (%(manufacturer)s, %(model_number)s, %(description)s, %(component_type_id)s, %(category_id)s, %(specifications)s, %(unit_price)s, %(currency)s, %(supplier)s, %(part_number)s, %(weight_kg)s, %(dimensions_json)s, %(is_active)s, %(is_preferred)s, %(stock_status)s, %(version)s, %(metadata_json)s, %(name)s, %(notes)s, %(created_at)s, %(updated_at)s, %(is_deleted)s, %(deleted_at)s, %(deleted_by_user_id)s) RETURNING components.id]
E   [parameters: {'manufacturer': 'Test Manufacturer', 'model_number': 'TEST-001', 'description': None, 'component_type_id': 2243, 'category_id': 3457, 'specifications': None, 'unit_price': None, 'currency': 'EUR', 'supplier': None, 'part_number': None, 'weight_kg': None, 'dimensions_json': None, 'is_active': True, 'is_preferred': False, 'stock_status': 'available', 'version': '1.0', 'metadata_json': None, 'name': 'Test Component', 'notes': None, 'created_at': datetime.datetime(2025, 8, 7, 21, 51, 50, 96348), 'updated_at': datetime.datetime(2025, 8, 7, 21, 51, 50, 96350), 'is_deleted': False, 'deleted_at': None, 'deleted_by_user_id': None}]
E   (Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-08 00:51:50,690 - ERROR - Test Failed: tests/core/models/test_component_relational.py::TestComponentRelationalModel::test_component_category_validation
Failure Details:
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1963: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py:943: in do_execute
    cursor.execute(statement, parameters)
E   psycopg2.errors.UniqueViolation: duplicate key value violates unique constraint "uq_component_manufacturer_model"
E   DETAIL:  Key (manufacturer, model_number, is_deleted)=(Test Manufacturer, TEST-001, f) already exists.

The above exception was the direct cause of the following exception:
tests/core/models/test_component_relational.py:113: in test_component_category_validation
    db_session.commit()
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:2032: in commit
    trans.commit(_to_root=True)
<string>:2: in commit
    ???
.venv/lib/python3.13/site-packages/sqlalchemy/orm/state_changes.py:139: in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:1313: in commit
    self._prepare_impl()
<string>:2: in _prepare_impl
    ???
.venv/lib/python3.13/site-packages/sqlalchemy/orm/state_changes.py:139: in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:1288: in _prepare_impl
    self.session.flush()
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4345: in flush
    self._flush(objects)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4480: in _flush
    with util.safe_reraise():
         ^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/util/langhelpers.py:224: in __exit__
    raise exc_value.with_traceback(exc_tb)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4441: in _flush
    flush_context.execute()
.venv/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py:466: in execute
    rec.execute(self)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py:642: in execute
    util.preloaded.orm_persistence.save_obj(
.venv/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py:93: in save_obj
    _emit_insert_statements(
.venv/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py:1233: in _emit_insert_statements
    result = connection.execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1415: in execute
    return meth(
.venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py:523: in _execute_on_connection
    return connection._execute_clauseelement(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1637: in _execute_clauseelement
    ret = self._execute_context(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1842: in _execute_context
    return self._exec_single_context(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1982: in _exec_single_context
    self._handle_dbapi_exception(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:2351: in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1963: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py:943: in do_execute
    cursor.execute(statement, parameters)
E   sqlalchemy.exc.IntegrityError: (psycopg2.errors.UniqueViolation) duplicate key value violates unique constraint "uq_component_manufacturer_model"
E   DETAIL:  Key (manufacturer, model_number, is_deleted)=(Test Manufacturer, TEST-001, f) already exists.
E   
E   [SQL: INSERT INTO components (manufacturer, model_number, description, component_type_id, category_id, specifications, unit_price, currency, supplier, part_number, weight_kg, dimensions_json, is_active, is_preferred, stock_status, version, metadata_json, name, notes, created_at, updated_at, is_deleted, deleted_at, deleted_by_user_id) VALUES (%(manufacturer)s, %(model_number)s, %(description)s, %(component_type_id)s, %(category_id)s, %(specifications)s, %(unit_price)s, %(currency)s, %(supplier)s, %(part_number)s, %(weight_kg)s, %(dimensions_json)s, %(is_active)s, %(is_preferred)s, %(stock_status)s, %(version)s, %(metadata_json)s, %(name)s, %(notes)s, %(created_at)s, %(updated_at)s, %(is_deleted)s, %(deleted_at)s, %(deleted_by_user_id)s) RETURNING components.id]
E   [parameters: {'manufacturer': 'Test Manufacturer', 'model_number': 'TEST-001', 'description': None, 'component_type_id': 2244, 'category_id': 3458, 'specifications': None, 'unit_price': None, 'currency': 'EUR', 'supplier': None, 'part_number': None, 'weight_kg': None, 'dimensions_json': None, 'is_active': True, 'is_preferred': False, 'stock_status': 'available', 'version': '1.0', 'metadata_json': None, 'name': 'Test Component', 'notes': None, 'created_at': datetime.datetime(2025, 8, 7, 21, 51, 50, 408146), 'updated_at': datetime.datetime(2025, 8, 7, 21, 51, 50, 408148), 'is_deleted': False, 'deleted_at': None, 'deleted_by_user_id': None}]
E   (Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-08 00:51:50,961 - ERROR - Test Failed: tests/core/models/test_component_relational.py::TestComponentRelationalModel::test_component_string_representations
Failure Details:
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1963: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py:943: in do_execute
    cursor.execute(statement, parameters)
E   psycopg2.errors.UniqueViolation: duplicate key value violates unique constraint "uq_component_manufacturer_model"
E   DETAIL:  Key (manufacturer, model_number, is_deleted)=(ABB, S203-B16, f) already exists.

The above exception was the direct cause of the following exception:
tests/core/models/test_component_relational.py:153: in test_component_string_representations
    db_session.commit()
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:2032: in commit
    trans.commit(_to_root=True)
<string>:2: in commit
    ???
.venv/lib/python3.13/site-packages/sqlalchemy/orm/state_changes.py:139: in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:1313: in commit
    self._prepare_impl()
<string>:2: in _prepare_impl
    ???
.venv/lib/python3.13/site-packages/sqlalchemy/orm/state_changes.py:139: in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:1288: in _prepare_impl
    self.session.flush()
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4345: in flush
    self._flush(objects)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4480: in _flush
    with util.safe_reraise():
         ^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/util/langhelpers.py:224: in __exit__
    raise exc_value.with_traceback(exc_tb)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4441: in _flush
    flush_context.execute()
.venv/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py:466: in execute
    rec.execute(self)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py:642: in execute
    util.preloaded.orm_persistence.save_obj(
.venv/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py:93: in save_obj
    _emit_insert_statements(
.venv/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py:1233: in _emit_insert_statements
    result = connection.execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1415: in execute
    return meth(
.venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py:523: in _execute_on_connection
    return connection._execute_clauseelement(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1637: in _execute_clauseelement
    ret = self._execute_context(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1842: in _execute_context
    return self._exec_single_context(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1982: in _exec_single_context
    self._handle_dbapi_exception(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:2351: in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1963: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py:943: in do_execute
    cursor.execute(statement, parameters)
E   sqlalchemy.exc.IntegrityError: (psycopg2.errors.UniqueViolation) duplicate key value violates unique constraint "uq_component_manufacturer_model"
E   DETAIL:  Key (manufacturer, model_number, is_deleted)=(ABB, S203-B16, f) already exists.
E   
E   [SQL: INSERT INTO components (manufacturer, model_number, description, component_type_id, category_id, specifications, unit_price, currency, supplier, part_number, weight_kg, dimensions_json, is_active, is_preferred, stock_status, version, metadata_json, name, notes, created_at, updated_at, is_deleted, deleted_at, deleted_by_user_id) VALUES (%(manufacturer)s, %(model_number)s, %(description)s, %(component_type_id)s, %(category_id)s, %(specifications)s, %(unit_price)s, %(currency)s, %(supplier)s, %(part_number)s, %(weight_kg)s, %(dimensions_json)s, %(is_active)s, %(is_preferred)s, %(stock_status)s, %(version)s, %(metadata_json)s, %(name)s, %(notes)s, %(created_at)s, %(updated_at)s, %(is_deleted)s, %(deleted_at)s, %(deleted_by_user_id)s) RETURNING components.id]
E   [parameters: {'manufacturer': 'ABB', 'model_number': 'S203-B16', 'description': None, 'component_type_id': 2245, 'category_id': 3459, 'specifications': None, 'unit_price': None, 'currency': 'EUR', 'supplier': None, 'part_number': None, 'weight_kg': None, 'dimensions_json': None, 'is_active': True, 'is_preferred': False, 'stock_status': 'available', 'version': '1.0', 'metadata_json': None, 'name': 'Test Circuit Breaker', 'notes': None, 'created_at': datetime.datetime(2025, 8, 7, 21, 51, 50, 723482), 'updated_at': datetime.datetime(2025, 8, 7, 21, 51, 50, 723484), 'is_deleted': False, 'deleted_at': None, 'deleted_by_user_id': None}]
E   (Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-08 00:51:51,200 - ERROR - Test Failed: tests/core/models/test_component_relational.py::TestComponentRelationalModel::test_component_with_specifications
Failure Details:
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1963: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py:943: in do_execute
    cursor.execute(statement, parameters)
E   psycopg2.errors.UniqueViolation: duplicate key value violates unique constraint "uq_component_manufacturer_model"
E   DETAIL:  Key (manufacturer, model_number, is_deleted)=(Schneider Electric, C60N-B16, f) already exists.

The above exception was the direct cause of the following exception:
tests/core/models/test_component_relational.py:188: in test_component_with_specifications
    db_session.commit()
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:2032: in commit
    trans.commit(_to_root=True)
<string>:2: in commit
    ???
.venv/lib/python3.13/site-packages/sqlalchemy/orm/state_changes.py:139: in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:1313: in commit
    self._prepare_impl()
<string>:2: in _prepare_impl
    ???
.venv/lib/python3.13/site-packages/sqlalchemy/orm/state_changes.py:139: in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:1288: in _prepare_impl
    self.session.flush()
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4345: in flush
    self._flush(objects)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4480: in _flush
    with util.safe_reraise():
         ^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/util/langhelpers.py:224: in __exit__
    raise exc_value.with_traceback(exc_tb)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4441: in _flush
    flush_context.execute()
.venv/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py:466: in execute
    rec.execute(self)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py:642: in execute
    util.preloaded.orm_persistence.save_obj(
.venv/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py:93: in save_obj
    _emit_insert_statements(
.venv/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py:1233: in _emit_insert_statements
    result = connection.execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1415: in execute
    return meth(
.venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py:523: in _execute_on_connection
    return connection._execute_clauseelement(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1637: in _execute_clauseelement
    ret = self._execute_context(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1842: in _execute_context
    return self._exec_single_context(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1982: in _exec_single_context
    self._handle_dbapi_exception(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:2351: in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1963: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py:943: in do_execute
    cursor.execute(statement, parameters)
E   sqlalchemy.exc.IntegrityError: (psycopg2.errors.UniqueViolation) duplicate key value violates unique constraint "uq_component_manufacturer_model"
E   DETAIL:  Key (manufacturer, model_number, is_deleted)=(Schneider Electric, C60N-B16, f) already exists.
E   
E   [SQL: INSERT INTO components (manufacturer, model_number, description, component_type_id, category_id, specifications, unit_price, currency, supplier, part_number, weight_kg, dimensions_json, is_active, is_preferred, stock_status, version, metadata_json, name, notes, created_at, updated_at, is_deleted, deleted_at, deleted_by_user_id) VALUES (%(manufacturer)s, %(model_number)s, %(description)s, %(component_type_id)s, %(category_id)s, %(specifications)s, %(unit_price)s, %(currency)s, %(supplier)s, %(part_number)s, %(weight_kg)s, %(dimensions_json)s, %(is_active)s, %(is_preferred)s, %(stock_status)s, %(version)s, %(metadata_json)s, %(name)s, %(notes)s, %(created_at)s, %(updated_at)s, %(is_deleted)s, %(deleted_at)s, %(deleted_by_user_id)s) RETURNING components.id]
E   [parameters: {'manufacturer': 'Schneider Electric', 'model_number': 'C60N-B16', 'description': None, 'component_type_id': 2246, 'category_id': 3460, 'specifications': '"{\\"electrical\\": {\\"voltage_rating\\": {\\"value\\": 400, \\"unit\\": \\"V\\"}, \\"current_rating\\": {\\"value\\": 16, \\"unit\\": \\"A\\"}}, \\"standards_compliance\\": [\\"IEC 60947-2\\"]}"', 'unit_price': None, 'currency': 'EUR', 'supplier': None, 'part_number': None, 'weight_kg': None, 'dimensions_json': None, 'is_active': True, 'is_preferred': False, 'stock_status': 'available', 'version': '1.0', 'metadata_json': None, 'name': 'Circuit Breaker with Specs', 'notes': None, 'created_at': datetime.datetime(2025, 8, 7, 21, 51, 50, 980509), 'updated_at': datetime.datetime(2025, 8, 7, 21, 51, 50, 980510), 'is_deleted': False, 'deleted_at': None, 'deleted_by_user_id': None}]
E   (Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-08 00:51:51,300 - ERROR - Test Failed: tests/core/models/test_synchronization_log_model.py::TestSynchronizationLog::test_synchronization_log_initialization_minimal
Failure Details:
tests/core/models/test_synchronization_log_model.py:39: in test_synchronization_log_initialization_minimal
    assert sync_log.status == SyncStatus.PENDING
E   assert None == <SyncStatus.PENDING: 'Pending'>
E    +  where None = <[AttributeError("'NoneType' object has no attribute 'value'") raised in repr()] SynchronizationLog object at 0x7f9d259fdbe0>.status
E    +  and   <SyncStatus.PENDING: 'Pending'> = SyncStatus.PENDING
2025-08-08 00:51:51,316 - ERROR - Test Failed: tests/core/models/test_synchronization_log_model.py::TestSynchronizationConflict::test_synchronization_conflict_initialization_minimal
Failure Details:
tests/core/models/test_synchronization_log_model.py:272: in test_synchronization_conflict_initialization_minimal
    assert conflict.is_resolved is False
E   AssertionError: assert None is False
E    +  where None = <SynchronizationConflict(id=None, entity_type='project', entity_id='123', conflict_type='field_conflict', is_resolved=None)>.is_resolved
2025-08-08 00:51:52,477 - ERROR - Test Failed: tests/core/repositories/test_component_repository.py::TestComponentRepository::test_get_by_manufacturer
Failure Details:
tests/core/repositories/test_component_repository.py:64: in test_get_by_manufacturer
    assert len(abb_components) == 2
E   assert 100 == 2
E    +  where 100 = len(<[MissingGreenlet("greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?") raised in repr()] list object at 0x7f9d1513fe00>)
2025-08-08 00:51:52,548 - ERROR - Test Failed: tests/core/repositories/test_component_repository.py::TestComponentRepository::test_get_by_part_number
Failure Details:
tests/core/repositories/test_component_repository.py:72: in test_get_by_part_number
    assert result is not None
E   assert None is not None
2025-08-08 00:51:52,758 - ERROR - Test Failed: tests/core/repositories/test_component_repository.py::TestComponentRepository::test_get_preferred_components
Failure Details:
tests/core/repositories/test_component_repository.py:85: in test_get_preferred_components
    assert len(preferred) == 2
E   assert 100 == 2
E    +  where 100 = len(<[MissingGreenlet("greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?") raised in repr()] list object at 0x7f9d16398300>)
2025-08-08 00:51:52,856 - ERROR - Test Failed: tests/core/repositories/test_component_repository.py::TestComponentRepository::test_search_components
Failure Details:
tests/core/repositories/test_component_repository.py:93: in test_search_components
    assert len(results) == 2
E   assert 100 == 2
E    +  where 100 = len(<[MissingGreenlet("greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?") raised in repr()] list object at 0x7f9d1680d900>)
2025-08-08 00:51:53,228 - ERROR - Test Failed: tests/core/repositories/test_component_repository.py::TestComponentRepository::test_count_active_components
Failure Details:
tests/core/repositories/test_component_repository.py:155: in test_count_active_components
    assert count == 4  # All components are active by default
    ^^^^^^^^^^^^^^^^^
E   assert 1062 == 4
2025-08-08 00:51:53,789 - ERROR - Test Failed: tests/core/repositories/test_component_repository.py::TestComponentRepository::test_get_components_paginated_with_search
Failure Details:
tests/core/repositories/test_component_repository.py:260: in test_get_components_paginated_with_search
    assert result.total == 2
E   assert 533 == 2
E    +  where 533 = <[MissingGreenlet("greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?") raised in repr()] PaginationResult object at 0x7f9d258b7130>.total
2025-08-08 00:51:53,623 - ERROR - Test Failed: tests/core/repositories/test_component_repository.py::TestComponentRepository::test_pagination_edge_cases
Failure Details:
tests/core/repositories/test_component_repository.py:303: in test_pagination_edge_cases
    assert result.total == 4
E   assert 1 == 4
E    +  where 1 = <[MissingGreenlet("greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?") raised in repr()] PaginationResult object at 0x7f9d24918e50>.total
2025-08-08 00:51:53,722 - ERROR - Test Failed: tests/core/repositories/test_component_repository.py::TestComponentRepository::test_search_case_insensitive
Failure Details:
tests/core/repositories/test_component_repository.py:315: in test_search_case_insensitive
    assert len(results_lower) == len(results_upper) == len(results_mixed) == 2
E   assert 100 == 2
E    +  where 100 = len(<[MissingGreenlet("greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?") raised in repr()] list object at 0x7f9d162d43c0>)
2025-08-08 00:51:53,804 - ERROR - Test Failed: tests/core/repositories/test_component_repository.py::TestComponentRepository::test_get_by_manufacturer_partial_match
Failure Details:
tests/core/repositories/test_component_repository.py:321: in test_get_by_manufacturer_partial_match
    assert len(results) == 1
E   assert 100 == 1
E    +  where 100 = len(<[MissingGreenlet("greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?") raised in repr()] list object at 0x7f9d150d7340>)
2025-08-08 00:51:55,439 - ERROR - Test Failed: tests/core/services/test_component_category_service.py::TestComponentCategoryService::test_create_category_success
Failure Details:
tests/core/services/test_component_category_service.py:74: in test_create_category_success
    assert result.description == "Test description"
E   AssertionError: assert 'Test category description' == 'Test description'
E     
E     [0m[91m- Test description[39;49;00m[90m[39;49;00m
E     [92m+ Test category description[39;49;00m[90m[39;49;00m
E     ?     +++++++++[90m[39;49;00m
2025-08-08 00:51:56,812 - CRITICAL - Error in setup: tests/core/services/test_project_member_service.py::TestProjectMemberService::test_add_member_to_project_duplicate_entry
Error Details:
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:545: in _prepare_and_execute
    self._rows = deque(await prepared_stmt.fetch(*parameters))
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/asyncpg/prepared_stmt.py:176: in fetch
    data = await self.__bind_execute(args, 0, timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/asyncpg/prepared_stmt.py:267: in __bind_execute
    data, status, _ = await self.__do_execute(
.venv/lib/python3.13/site-packages/asyncpg/prepared_stmt.py:256: in __do_execute
    return await executor(protocol)
           ^^^^^^^^^^^^^^^^^^^^^^^^
asyncpg/protocol/protocol.pyx:206: in bind_execute
    ???
E   asyncpg.exceptions.ForeignKeyViolationError: insert or update on table "project_members" violates foreign key constraint "ProjectMember_role_id_fkey"
E   DETAIL:  Key (role_id)=(640) is not present in table "user_roles".

The above exception was the direct cause of the following exception:
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1963: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py:943: in do_execute
    cursor.execute(statement, parameters)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:580: in execute
    self._adapt_connection.await_(
.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py:132: in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py:196: in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:558: in _prepare_and_execute
    self._handle_exception(error)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:508: in _handle_exception
    self._adapt_connection._handle_exception(error)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:792: in _handle_exception
    raise translated_error from error
E   sqlalchemy.dialects.postgresql.asyncpg.AsyncAdapt_asyncpg_dbapi.IntegrityError: <class 'asyncpg.exceptions.ForeignKeyViolationError'>: insert or update on table "project_members" violates foreign key constraint "ProjectMember_role_id_fkey"
E   DETAIL:  Key (role_id)=(640) is not present in table "user_roles".

The above exception was the direct cause of the following exception:
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:696: in pytest_fixture_setup
    hook_result = yield
                  ^^^^^
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:321: in _async_fixture_wrapper
    result = runner.run(setup(), context=context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py:118: in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py:725: in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:317: in setup
    res = await fixture_function(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/conftest.py:910: in test_project_member
    member = await member_service.add_member_to_project(async_test_project.id, member_create)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/monitoring/unified_performance_monitor.py:263: in async_wrapper
    return await result
           ^^^^^^^^^^^^
src/core/services/general/project_member_service.py:86: in add_member_to_project
    new_member = await self.project_member_repo.create(member_dict)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/monitoring/unified_performance_monitor.py:263: in async_wrapper
    return await result
           ^^^^^^^^^^^^
src/core/repositories/base_repository.py:75: in create
    await self.db_session.flush()
.venv/lib/python3.13/site-packages/sqlalchemy/ext/asyncio/session.py:801: in flush
    await greenlet_spawn(self.sync_session.flush, objects=objects)
.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py:203: in greenlet_spawn
    result = context.switch(value)
             ^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4345: in flush
    self._flush(objects)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4480: in _flush
    with util.safe_reraise():
         ^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/util/langhelpers.py:224: in __exit__
    raise exc_value.with_traceback(exc_tb)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4441: in _flush
    flush_context.execute()
.venv/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py:466: in execute
    rec.execute(self)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py:642: in execute
    util.preloaded.orm_persistence.save_obj(
.venv/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py:93: in save_obj
    _emit_insert_statements(
.venv/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py:1233: in _emit_insert_statements
    result = connection.execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1415: in execute
    return meth(
.venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py:523: in _execute_on_connection
    return connection._execute_clauseelement(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1637: in _execute_clauseelement
    ret = self._execute_context(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1842: in _execute_context
    return self._exec_single_context(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1982: in _exec_single_context
    self._handle_dbapi_exception(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:2351: in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1963: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py:943: in do_execute
    cursor.execute(statement, parameters)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:580: in execute
    self._adapt_connection.await_(
.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py:132: in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py:196: in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:558: in _prepare_and_execute
    self._handle_exception(error)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:508: in _handle_exception
    self._adapt_connection._handle_exception(error)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:792: in _handle_exception
    raise translated_error from error
E   sqlalchemy.exc.IntegrityError: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.ForeignKeyViolationError'>: insert or update on table "project_members" violates foreign key constraint "ProjectMember_role_id_fkey"
E   DETAIL:  Key (role_id)=(640) is not present in table "user_roles".
E   [SQL: INSERT INTO project_members (name, user_id, project_id, role_id, is_active, assigned_at, expires_at, notes, created_at, updated_at, is_deleted, deleted_at, deleted_by_user_id) VALUES ($1::VARCHAR, $2::INTEGER, $3::INTEGER, $4::INTEGER, $5::BOOLEAN, $6::TIMESTAMP WITHOUT TIME ZONE, $7::TIMESTAMP WITHOUT TIME ZONE, $8::VARCHAR, $9::TIMESTAMP WITHOUT TIME ZONE, $10::TIMESTAMP WITHOUT TIME ZONE, $11::BOOLEAN, $12::TIMESTAMP WITHOUT TIME ZONE, $13::INTEGER) RETURNING project_members.id]
E   [parameters: ('Test Member', 1112029, 3277, 640, True, datetime.datetime(2025, 8, 7, 21, 51, 56, 542428), None, None, datetime.datetime(2025, 8, 7, 21, 51, 56, 542432), datetime.datetime(2025, 8, 7, 21, 51, 56, 542435), False, None, None)]
E   (Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-08 00:51:57,316 - CRITICAL - Error in setup: tests/core/services/test_project_member_service.py::TestProjectMemberService::test_remove_member_from_project_success
Error Details:
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:545: in _prepare_and_execute
    self._rows = deque(await prepared_stmt.fetch(*parameters))
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/asyncpg/prepared_stmt.py:176: in fetch
    data = await self.__bind_execute(args, 0, timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/asyncpg/prepared_stmt.py:267: in __bind_execute
    data, status, _ = await self.__do_execute(
.venv/lib/python3.13/site-packages/asyncpg/prepared_stmt.py:256: in __do_execute
    return await executor(protocol)
           ^^^^^^^^^^^^^^^^^^^^^^^^
asyncpg/protocol/protocol.pyx:206: in bind_execute
    ???
E   asyncpg.exceptions.ForeignKeyViolationError: insert or update on table "project_members" violates foreign key constraint "ProjectMember_role_id_fkey"
E   DETAIL:  Key (role_id)=(641) is not present in table "user_roles".

The above exception was the direct cause of the following exception:
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1963: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py:943: in do_execute
    cursor.execute(statement, parameters)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:580: in execute
    self._adapt_connection.await_(
.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py:132: in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py:196: in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:558: in _prepare_and_execute
    self._handle_exception(error)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:508: in _handle_exception
    self._adapt_connection._handle_exception(error)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:792: in _handle_exception
    raise translated_error from error
E   sqlalchemy.dialects.postgresql.asyncpg.AsyncAdapt_asyncpg_dbapi.IntegrityError: <class 'asyncpg.exceptions.ForeignKeyViolationError'>: insert or update on table "project_members" violates foreign key constraint "ProjectMember_role_id_fkey"
E   DETAIL:  Key (role_id)=(641) is not present in table "user_roles".

The above exception was the direct cause of the following exception:
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:696: in pytest_fixture_setup
    hook_result = yield
                  ^^^^^
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:321: in _async_fixture_wrapper
    result = runner.run(setup(), context=context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py:118: in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py:725: in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:317: in setup
    res = await fixture_function(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/conftest.py:910: in test_project_member
    member = await member_service.add_member_to_project(async_test_project.id, member_create)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/monitoring/unified_performance_monitor.py:263: in async_wrapper
    return await result
           ^^^^^^^^^^^^
src/core/services/general/project_member_service.py:86: in add_member_to_project
    new_member = await self.project_member_repo.create(member_dict)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/monitoring/unified_performance_monitor.py:263: in async_wrapper
    return await result
           ^^^^^^^^^^^^
src/core/repositories/base_repository.py:75: in create
    await self.db_session.flush()
.venv/lib/python3.13/site-packages/sqlalchemy/ext/asyncio/session.py:801: in flush
    await greenlet_spawn(self.sync_session.flush, objects=objects)
.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py:203: in greenlet_spawn
    result = context.switch(value)
             ^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4345: in flush
    self._flush(objects)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4480: in _flush
    with util.safe_reraise():
         ^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/util/langhelpers.py:224: in __exit__
    raise exc_value.with_traceback(exc_tb)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4441: in _flush
    flush_context.execute()
.venv/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py:466: in execute
    rec.execute(self)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py:642: in execute
    util.preloaded.orm_persistence.save_obj(
.venv/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py:93: in save_obj
    _emit_insert_statements(
.venv/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py:1233: in _emit_insert_statements
    result = connection.execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1415: in execute
    return meth(
.venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py:523: in _execute_on_connection
    return connection._execute_clauseelement(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1637: in _execute_clauseelement
    ret = self._execute_context(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1842: in _execute_context
    return self._exec_single_context(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1982: in _exec_single_context
    self._handle_dbapi_exception(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:2351: in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1963: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py:943: in do_execute
    cursor.execute(statement, parameters)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:580: in execute
    self._adapt_connection.await_(
.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py:132: in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py:196: in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:558: in _prepare_and_execute
    self._handle_exception(error)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:508: in _handle_exception
    self._adapt_connection._handle_exception(error)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:792: in _handle_exception
    raise translated_error from error
E   sqlalchemy.exc.IntegrityError: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.ForeignKeyViolationError'>: insert or update on table "project_members" violates foreign key constraint "ProjectMember_role_id_fkey"
E   DETAIL:  Key (role_id)=(641) is not present in table "user_roles".
E   [SQL: INSERT INTO project_members (name, user_id, project_id, role_id, is_active, assigned_at, expires_at, notes, created_at, updated_at, is_deleted, deleted_at, deleted_by_user_id) VALUES ($1::VARCHAR, $2::INTEGER, $3::INTEGER, $4::INTEGER, $5::BOOLEAN, $6::TIMESTAMP WITHOUT TIME ZONE, $7::TIMESTAMP WITHOUT TIME ZONE, $8::VARCHAR, $9::TIMESTAMP WITHOUT TIME ZONE, $10::TIMESTAMP WITHOUT TIME ZONE, $11::BOOLEAN, $12::TIMESTAMP WITHOUT TIME ZONE, $13::INTEGER) RETURNING project_members.id]
E   [parameters: ('Test Member', 1112030, 3279, 641, True, datetime.datetime(2025, 8, 7, 21, 51, 57, 35617), None, None, datetime.datetime(2025, 8, 7, 21, 51, 57, 35621), datetime.datetime(2025, 8, 7, 21, 51, 57, 35624), False, None, None)]
E   (Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-08 00:51:58,016 - CRITICAL - Error in setup: tests/core/services/test_project_member_service.py::TestProjectMemberService::test_update_project_member_success
Error Details:
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:545: in _prepare_and_execute
    self._rows = deque(await prepared_stmt.fetch(*parameters))
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/asyncpg/prepared_stmt.py:176: in fetch
    data = await self.__bind_execute(args, 0, timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/asyncpg/prepared_stmt.py:267: in __bind_execute
    data, status, _ = await self.__do_execute(
.venv/lib/python3.13/site-packages/asyncpg/prepared_stmt.py:256: in __do_execute
    return await executor(protocol)
           ^^^^^^^^^^^^^^^^^^^^^^^^
asyncpg/protocol/protocol.pyx:206: in bind_execute
    ???
E   asyncpg.exceptions.ForeignKeyViolationError: insert or update on table "project_members" violates foreign key constraint "ProjectMember_role_id_fkey"
E   DETAIL:  Key (role_id)=(642) is not present in table "user_roles".

The above exception was the direct cause of the following exception:
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1963: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py:943: in do_execute
    cursor.execute(statement, parameters)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:580: in execute
    self._adapt_connection.await_(
.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py:132: in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py:196: in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:558: in _prepare_and_execute
    self._handle_exception(error)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:508: in _handle_exception
    self._adapt_connection._handle_exception(error)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:792: in _handle_exception
    raise translated_error from error
E   sqlalchemy.dialects.postgresql.asyncpg.AsyncAdapt_asyncpg_dbapi.IntegrityError: <class 'asyncpg.exceptions.ForeignKeyViolationError'>: insert or update on table "project_members" violates foreign key constraint "ProjectMember_role_id_fkey"
E   DETAIL:  Key (role_id)=(642) is not present in table "user_roles".

The above exception was the direct cause of the following exception:
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:696: in pytest_fixture_setup
    hook_result = yield
                  ^^^^^
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:321: in _async_fixture_wrapper
    result = runner.run(setup(), context=context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py:118: in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py:725: in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:317: in setup
    res = await fixture_function(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/conftest.py:910: in test_project_member
    member = await member_service.add_member_to_project(async_test_project.id, member_create)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/monitoring/unified_performance_monitor.py:263: in async_wrapper
    return await result
           ^^^^^^^^^^^^
src/core/services/general/project_member_service.py:86: in add_member_to_project
    new_member = await self.project_member_repo.create(member_dict)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/monitoring/unified_performance_monitor.py:263: in async_wrapper
    return await result
           ^^^^^^^^^^^^
src/core/repositories/base_repository.py:75: in create
    await self.db_session.flush()
.venv/lib/python3.13/site-packages/sqlalchemy/ext/asyncio/session.py:801: in flush
    await greenlet_spawn(self.sync_session.flush, objects=objects)
.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py:203: in greenlet_spawn
    result = context.switch(value)
             ^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4345: in flush
    self._flush(objects)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4480: in _flush
    with util.safe_reraise():
         ^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/util/langhelpers.py:224: in __exit__
    raise exc_value.with_traceback(exc_tb)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4441: in _flush
    flush_context.execute()
.venv/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py:466: in execute
    rec.execute(self)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py:642: in execute
    util.preloaded.orm_persistence.save_obj(
.venv/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py:93: in save_obj
    _emit_insert_statements(
.venv/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py:1233: in _emit_insert_statements
    result = connection.execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1415: in execute
    return meth(
.venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py:523: in _execute_on_connection
    return connection._execute_clauseelement(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1637: in _execute_clauseelement
    ret = self._execute_context(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1842: in _execute_context
    return self._exec_single_context(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1982: in _exec_single_context
    self._handle_dbapi_exception(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:2351: in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1963: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py:943: in do_execute
    cursor.execute(statement, parameters)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:580: in execute
    self._adapt_connection.await_(
.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py:132: in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py:196: in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:558: in _prepare_and_execute
    self._handle_exception(error)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:508: in _handle_exception
    self._adapt_connection._handle_exception(error)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:792: in _handle_exception
    raise translated_error from error
E   sqlalchemy.exc.IntegrityError: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.ForeignKeyViolationError'>: insert or update on table "project_members" violates foreign key constraint "ProjectMember_role_id_fkey"
E   DETAIL:  Key (role_id)=(642) is not present in table "user_roles".
E   [SQL: INSERT INTO project_members (name, user_id, project_id, role_id, is_active, assigned_at, expires_at, notes, created_at, updated_at, is_deleted, deleted_at, deleted_by_user_id) VALUES ($1::VARCHAR, $2::INTEGER, $3::INTEGER, $4::INTEGER, $5::BOOLEAN, $6::TIMESTAMP WITHOUT TIME ZONE, $7::TIMESTAMP WITHOUT TIME ZONE, $8::VARCHAR, $9::TIMESTAMP WITHOUT TIME ZONE, $10::TIMESTAMP WITHOUT TIME ZONE, $11::BOOLEAN, $12::TIMESTAMP WITHOUT TIME ZONE, $13::INTEGER) RETURNING project_members.id]
E   [parameters: ('Test Member', 1112032, 3281, 642, True, datetime.datetime(2025, 8, 7, 21, 51, 57, 756519), None, None, datetime.datetime(2025, 8, 7, 21, 51, 57, 756523), datetime.datetime(2025, 8, 7, 21, 51, 57, 756525), False, None, None)]
E   (Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-08 00:51:58,533 - CRITICAL - Error in setup: tests/core/services/test_project_member_service.py::TestProjectMemberService::test_list_project_members_success
Error Details:
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:545: in _prepare_and_execute
    self._rows = deque(await prepared_stmt.fetch(*parameters))
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/asyncpg/prepared_stmt.py:176: in fetch
    data = await self.__bind_execute(args, 0, timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/asyncpg/prepared_stmt.py:267: in __bind_execute
    data, status, _ = await self.__do_execute(
.venv/lib/python3.13/site-packages/asyncpg/prepared_stmt.py:256: in __do_execute
    return await executor(protocol)
           ^^^^^^^^^^^^^^^^^^^^^^^^
asyncpg/protocol/protocol.pyx:206: in bind_execute
    ???
E   asyncpg.exceptions.ForeignKeyViolationError: insert or update on table "project_members" violates foreign key constraint "ProjectMember_role_id_fkey"
E   DETAIL:  Key (role_id)=(644) is not present in table "user_roles".

The above exception was the direct cause of the following exception:
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1963: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py:943: in do_execute
    cursor.execute(statement, parameters)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:580: in execute
    self._adapt_connection.await_(
.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py:132: in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py:196: in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:558: in _prepare_and_execute
    self._handle_exception(error)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:508: in _handle_exception
    self._adapt_connection._handle_exception(error)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:792: in _handle_exception
    raise translated_error from error
E   sqlalchemy.dialects.postgresql.asyncpg.AsyncAdapt_asyncpg_dbapi.IntegrityError: <class 'asyncpg.exceptions.ForeignKeyViolationError'>: insert or update on table "project_members" violates foreign key constraint "ProjectMember_role_id_fkey"
E   DETAIL:  Key (role_id)=(644) is not present in table "user_roles".

The above exception was the direct cause of the following exception:
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:696: in pytest_fixture_setup
    hook_result = yield
                  ^^^^^
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:321: in _async_fixture_wrapper
    result = runner.run(setup(), context=context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py:118: in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py:725: in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/pytest_asyncio/plugin.py:317: in setup
    res = await fixture_function(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/conftest.py:910: in test_project_member
    member = await member_service.add_member_to_project(async_test_project.id, member_create)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/monitoring/unified_performance_monitor.py:263: in async_wrapper
    return await result
           ^^^^^^^^^^^^
src/core/services/general/project_member_service.py:86: in add_member_to_project
    new_member = await self.project_member_repo.create(member_dict)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/monitoring/unified_performance_monitor.py:263: in async_wrapper
    return await result
           ^^^^^^^^^^^^
src/core/repositories/base_repository.py:75: in create
    await self.db_session.flush()
.venv/lib/python3.13/site-packages/sqlalchemy/ext/asyncio/session.py:801: in flush
    await greenlet_spawn(self.sync_session.flush, objects=objects)
.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py:203: in greenlet_spawn
    result = context.switch(value)
             ^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4345: in flush
    self._flush(objects)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4480: in _flush
    with util.safe_reraise():
         ^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/util/langhelpers.py:224: in __exit__
    raise exc_value.with_traceback(exc_tb)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/session.py:4441: in _flush
    flush_context.execute()
.venv/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py:466: in execute
    rec.execute(self)
.venv/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py:642: in execute
    util.preloaded.orm_persistence.save_obj(
.venv/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py:93: in save_obj
    _emit_insert_statements(
.venv/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py:1233: in _emit_insert_statements
    result = connection.execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1415: in execute
    return meth(
.venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py:523: in _execute_on_connection
    return connection._execute_clauseelement(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1637: in _execute_clauseelement
    ret = self._execute_context(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1842: in _execute_context
    return self._exec_single_context(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1982: in _exec_single_context
    self._handle_dbapi_exception(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:2351: in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py:1963: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py:943: in do_execute
    cursor.execute(statement, parameters)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:580: in execute
    self._adapt_connection.await_(
.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py:132: in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py:196: in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:558: in _prepare_and_execute
    self._handle_exception(error)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:508: in _handle_exception
    self._adapt_connection._handle_exception(error)
.venv/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py:792: in _handle_exception
    raise translated_error from error
E   sqlalchemy.exc.IntegrityError: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.ForeignKeyViolationError'>: insert or update on table "project_members" violates foreign key constraint "ProjectMember_role_id_fkey"
E   DETAIL:  Key (role_id)=(644) is not present in table "user_roles".
E   [SQL: INSERT INTO project_members (name, user_id, project_id, role_id, is_active, assigned_at, expires_at, notes, created_at, updated_at, is_deleted, deleted_at, deleted_by_user_id) VALUES ($1::VARCHAR, $2::INTEGER, $3::INTEGER, $4::INTEGER, $5::BOOLEAN, $6::TIMESTAMP WITHOUT TIME ZONE, $7::TIMESTAMP WITHOUT TIME ZONE, $8::VARCHAR, $9::TIMESTAMP WITHOUT TIME ZONE, $10::TIMESTAMP WITHOUT TIME ZONE, $11::BOOLEAN, $12::TIMESTAMP WITHOUT TIME ZONE, $13::INTEGER) RETURNING project_members.id]
E   [parameters: ('Test Member', 1112033, 3283, 644, True, datetime.datetime(2025, 8, 7, 21, 51, 58, 257973), None, None, datetime.datetime(2025, 8, 7, 21, 51, 58, 257978), datetime.datetime(2025, 8, 7, 21, 51, 58, 257980), False, None, None)]
E   (Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-08 00:51:58,568 - ERROR - Test Failed: tests/core/services/test_project_service.py::TestProjectService::test_create_and_update_project_with_offline_status
Failure Details:
tests/core/services/test_project_service.py:27: in test_create_and_update_project_with_offline_status
    created_project = await project_service.create_project(create_data)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/monitoring/unified_performance_monitor.py:263: in async_wrapper
    return await result
           ^^^^^^^^^^^^
src/core/services/general/project_service.py:62: in create_project
    await self._validate_project_creation(project_data)
src/core/services/general/project_service.py:313: in _validate_project_creation
    raise DataValidationError(details={"validation_errors": validation_errors})
E   src.core.errors.exceptions.DataValidationError: Input validation failed.
2025-08-08 00:52:00,357 - ERROR - Test Failed: tests/test_cleanup_utilities.py::TestCleanupUtilities::test_transaction_state_tracking
Failure Details:
tests/test_cleanup_utilities.py:155: in test_transaction_state_tracking
    assert after_add_new > initial_new
E   assert 0 > 0
2025-08-08 00:52:00,622 - ERROR - Test Failed: tests/validation/test_advanced_validators.py::TestAdvancedElectricalValidator::test_invalid_power_factor
Failure Details:
tests/validation/test_advanced_validators.py:117: in test_invalid_power_factor
    assert any("power factor" in error.lower() for error in result.errors)
E   assert False
E    +  where False = any(<generator object TestAdvancedElectricalValidator.test_invalid_power_factor.<locals>.<genexpr> at 0x7f9d1589f920>)
2025-08-08 00:52:00,646 - ERROR - Test Failed: tests/validation/test_advanced_validators.py::TestValidationIntegration::test_full_project_validation_workflow
Failure Details:
tests/validation/test_advanced_validators.py:386: in test_full_project_validation_workflow
    assert len(errors) == 0
E   AssertionError: assert 1 == 0
E    +  where 1 = len([DependencyValidationResult(is_valid=False, severity=<ValidationSeverity.ERROR: 'error'>, message='Circular dependency detected: comp-2 \u2192 comp-1', affected_entities=['comp-2', 'comp-1'], suggested_actions=['Review component interconnections', 'Break circular dependency with buffer or isolator'], metadata={'cycle': ['comp-2', 'comp-1'], 'rule': 'circular_dependency'})])
2025-08-08 00:52:00,664 - ERROR - Test Failed: tests/validation/test_compatibility_matrix.py::TestCompatibilityMatrix::test_voltage_incompatibility
Failure Details:
tests/validation/test_compatibility_matrix.py:116: in test_voltage_incompatibility
    assert electrical_score.score < 0.5
E   AssertionError: assert 0.65 < 0.5
E    +  where 0.65 = CompatibilityScore(dimension=<CompatibilityDimension.ELECTRICAL: 'electrical'>, level=<CompatibilityLevel.MARGINAL: 'marginal'>, score=0.65, details={'voltage_score': 0.0, 'current_score': 1.0, 'power_score': 1.0, 'frequency_score': 1.0, 'project_voltage': 480, 'component_voltage': 240}, recommendations=['Consider component with 480V rating']).score
2025-08-08 00:52:00,669 - ERROR - Test Failed: tests/validation/test_compatibility_matrix.py::TestCompatibilityMatrix::test_current_capacity_warning
Failure Details:
tests/validation/test_compatibility_matrix.py:136: in test_current_capacity_warning
    assert electrical_score.score < 0.8  # Should be marginal due to capacity
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
E   AssertionError: assert 0.8716666666666667 < 0.8
E    +  where 0.8716666666666667 = CompatibilityScore(dimension=<CompatibilityDimension.ELECTRICAL: 'electrical'>, level=<CompatibilityLevel.COMPATIBLE: 'compatible'>, score=0.8716666666666667, details={'voltage_score': 1.0, 'current_score': 0.6333333333333333, 'power_score': 1.0, 'frequency_score': 1.0, 'project_voltage': 480, 'component_voltage': 480}, recommendations=['Upgrade to component with 187.5A rating']).score
2025-08-08 00:52:00,674 - ERROR - Test Failed: tests/validation/test_compatibility_matrix.py::TestCompatibilityMatrix::test_environmental_rating_compatibility
Failure Details:
tests/validation/test_compatibility_matrix.py:160: in test_environmental_rating_compatibility
    assert environmental_score.score < 0.8  # Should be marginal or incompatible
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
E   AssertionError: assert 0.8300000000000001 < 0.8
E    +  where 0.8300000000000001 = CompatibilityScore(dimension=<CompatibilityDimension.ENVIRONMENTAL: 'environmental'>, level=<CompatibilityLevel.COMPATIBLE: 'compatible'>, score=0.8300000000000001, details={'ip_score': 0.81, 'temp_score': 0.75, 'humidity_score': 1.0, 'project_environment': {'ip_rating': 'IP54', 'min_temperature': -20, 'max_temperature': 40, 'max_humidity': 85}, 'component_rating': {'ip_rating': 'IP44', 'min_operating_temp': -10, 'max_operating_temp': 50, 'max_humidity': 90}}, recommendations=['Consider temperature control measures']).score
2025-08-08 00:52:00,697 - ERROR - Test Failed: tests/validation/test_compatibility_matrix.py::TestCompatibilityMatrix::test_optimization_suggestions
Failure Details:
tests/validation/test_compatibility_matrix.py:411: in test_optimization_suggestions
    assert any(
E   assert False
E    +  where False = any(<generator object TestCompatibilityMatrix.test_optimization_suggestions.<locals>.<genexpr> at 0x7f9d144bcc70>)
2025-08-08 00:52:00,720 - ERROR - Test Failed: tests/validation/test_data_format_validator.py::TestMultiFormatDataValidator::test_electrical_validation_consistency
Failure Details:
tests/validation/test_data_format_validator.py:199: in test_electrical_validation_consistency
    assert warnings_found or any(
E   assert (False or False)
E    +  where False = any(<generator object TestMultiFormatDataValidator.test_electrical_validation_consistency.<locals>.<genexpr> at 0x7f9d15254ac0>)
2025-08-08 00:52:00,776 - ERROR - Test Failed: tests/validation/test_json_schema_validator.py::TestJsonSchemaValidator::test_missing_required_fields
Failure Details:
tests/validation/test_json_schema_validator.py:94: in test_missing_required_fields
    assert result.data_quality_score < 0.5
E   assert 0.9 < 0.5
E    +  where 0.9 = ValidationResult(is_valid=False, errors=[ValidationError(path='voltage_rating', message="Required field 'voltage_rating' is missing", value=None, expected_type='present field', constraint='field must exist', severity='error', suggestion="Add required field 'voltage_rating'", json_path='$.voltage_rating')], warnings=[], data_quality_score=0.9, processed_paths=['id', 'type', 'current_rating', 'power_rating', 'frequency_rating', 'environmental_rating', 'compliance_standards'], validation_time_ms=0.0, schema_version='basic').data_quality_score
2025-08-08 00:52:00,883 - ERROR - Test Failed: tests/validation/test_json_schema_validator.py::TestJsonSchemaValidator::test_array_validation
Failure Details:
tests/validation/test_json_schema_validator.py:290: in test_array_validation
    assert result.is_valid is False
E   AssertionError: assert True is False
E    +  where True = ValidationResult(is_valid=True, errors=[], warnings=[], data_quality_score=1.0, processed_paths=['id', 'type', 'voltage_rating', 'current_rating', 'power_rating', 'frequency_rating', 'environmental_rating', 'compliance_standards'], validation_time_ms=0.0, schema_version='basic').is_valid
2025-08-08 00:52:00,940 - ERROR - Test Failed: tests/validation/test_legacy_migration_validator.py::TestLegacyMigrationValidator::test_xml_v1_migration
Failure Details:
tests/validation/test_legacy_migration_validator.py:120: in test_xml_v1_migration
    assert result.migration_status == MigrationStatus.COMPLETED
E   assert <MigrationStatus.FAILED: 'failed'> == <MigrationStatus.COMPLETED: 'completed'>
E    +  where <MigrationStatus.FAILED: 'failed'> = MigrationValidationResult(source_format=<LegacyFormatType.XML_V1: 'xml_v1'>, target_format=<DataFormat.JSON: 'json'>, migration_status=<MigrationStatus.FAILED: 'failed'>, total_records=0, migrated_records=0, failed_records=0, validation_errors=[{'error': "could not convert string to float: ''", 'severity': 'error', 'path': 'migration'}], data_quality_score=0.0, warnings=["Migration failed: could not convert string to float: ''"], recommendations=['Review source data format and content'], processing_time_ms=0.22699999999999998, source_checksum='', target_checksum='', field_mapping={}, data_loss=['migration_failed'], metadata={'error': "could not convert string to float: ''"}).migration_status
E    +  and   <MigrationStatus.COMPLETED: 'completed'> = MigrationStatus.COMPLETED
2025-08-08 00:52:00,953 - ERROR - Test Failed: tests/validation/test_legacy_migration_validator.py::TestLegacyMigrationValidator::test_negative_values_in_legacy_data
Failure Details:
tests/validation/test_legacy_migration_validator.py:204: in test_negative_values_in_legacy_data
    assert any(
E   assert False
E    +  where False = any(<generator object TestLegacyMigrationValidator.test_negative_values_in_legacy_data.<locals>.<genexpr> at 0x7f9d156263e0>)
2025-08-08 00:52:00,957 - ERROR - Test Failed: tests/validation/test_legacy_migration_validator.py::TestLegacyMigrationValidator::test_custom_field_mapping
Failure Details:
tests/validation/test_legacy_migration_validator.py:229: in test_custom_field_mapping
    assert "custom_attribute" in result.field_mapping
E   AssertionError: assert 'custom_attribute' in {'VOLT': 'voltage_rating', 'CURR': 'current_rating', 'POW': 'power_rating', 'FREQ': 'frequency_rating', 'TYPE': 'component_type', 'CAT': 'category', 'DESC': 'description', 'MFG': 'manufacturer', 'MODEL': 'model_number', 'IP': 'ip_rating', 'CUSTOM_FIELD': 'custom_attribute'}
E    +  where {'VOLT': 'voltage_rating', 'CURR': 'current_rating', 'POW': 'power_rating', 'FREQ': 'frequency_rating', 'TYPE': 'component_type', 'CAT': 'category', 'DESC': 'description', 'MFG': 'manufacturer', 'MODEL': 'model_number', 'IP': 'ip_rating', 'CUSTOM_FIELD': 'custom_attribute'} = MigrationValidationResult(source_format=<LegacyFormatType.OLD_CSV: 'old_csv'>, target_format=<DataFormat.JSON: 'json'>, migration_status=<MigrationStatus.COMPLETED: 'completed'>, total_records=1, migrated_records=1, failed_records=0, validation_errors=[], data_quality_score=1.0, warnings=[], recommendations=['Perform test migration with sample data', 'Validate migrated data against business rules'], processing_time_ms=0.085, source_checksum='e23bf6237070d30fb5937d111310405c', target_checksum='5b55b80f627f64616b771dc0222694d8', field_mapping={'VOLT': 'voltage_rating', 'CURR': 'current_rating', 'POW': 'power_rating', 'FREQ': 'frequency_rating', 'TYPE': 'component_type', 'CAT': 'category', 'DESC': 'description', 'MFG': 'manufacturer', 'MODEL': 'model_number', 'IP': 'ip_rating', 'CUSTOM_FIELD': 'custom_attribute'}, data_loss=[], metadata={'source_format_version': 'legacy', 'target_format_version': 'modern', 'transformation_rules_applied': ['add_ip_prefix', 'split_standards', 'parse_temp_range', 'convert_voltage_unit', 'convert_current_unit', 'convert_power_unit'], 'validation_level': 'complete'}).field_mapping
2025-08-08 00:52:00,967 - ERROR - Test Failed: tests/validation/test_legacy_migration_validator.py::TestLegacyMigrationValidator::test_unit_conversion_in_legacy_data
Failure Details:
tests/validation/test_legacy_migration_validator.py:286: in test_unit_conversion_in_legacy_data
    assert result.migration_status == MigrationStatus.COMPLETED
E   AssertionError: assert <MigrationStatus.PARTIAL_SUCCESS: 'partial_success'> == <MigrationStatus.COMPLETED: 'completed'>
E    +  where <MigrationStatus.PARTIAL_SUCCESS: 'partial_success'> = MigrationValidationResult(source_format=<LegacyFormatType.OLD_CSV: 'old_csv'>, target_format=<DataFormat.JSON: 'json'>, migration_status=<MigrationStatus.PARTIAL_SUCCESS: 'partial_success'>, total_records=1, migrated_records=1, failed_records=0, validation_errors=[], data_quality_score=1.0, warnings=[], recommendations=['Fix source data integrity issues before migration', 'Perform test migration with sample data', 'Validate migrated data against business rules'], processing_time_ms=0.08399999999999999, source_checksum='799a091b17e5d1848c06c2c9ce8bd2a5', target_checksum='bb7823fbf8c939e9f0b3c6deb5fb3087', field_mapping={'VOLT': 'voltage_rating', 'CURR': 'current_rating', 'POW': 'power_rating', 'FREQ': 'frequency_rating', 'TYPE': 'component_type', 'CAT': 'category', 'DESC': 'description', 'MFG': 'manufacturer', 'MODEL': 'model_number', 'IP': 'ip_rating'}, data_loss=[], metadata={'source_format_version': 'legacy', 'target_format_version': 'modern', 'transformation_rules_applied': ['add_ip_prefix', 'split_standards', 'parse_temp_range', 'convert_voltage_unit', 'convert_current_unit', 'convert_power_unit'], 'validation_level': 'complete'}).migration_status
E    +  and   <MigrationStatus.COMPLETED: 'completed'> = MigrationStatus.COMPLETED
